<template>
  <div class="bundle-form-container">
    <!-- 表单头部 -->
    <div class="form-header">
      <div class="header-content">
        <div class="header-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="header-text">
          <h2>{{ $t('app.title') }}</h2>
          <p class="subtitle">{{ $t('app.subtitle') }}</p>
        </div>
      </div>
    </div>

    <!-- 主表单区域 -->
    <div class="form-main">
      <form @submit.prevent="handleSubmit" class="form-grid">
        <!-- 左侧：基本信息 -->
        <div class="form-section basic-info">
          <h3 class="section-title">
            <span class="section-icon">📱</span>
            {{ $t('form.basicInfo') || 'Basic Information' }}
          </h3>

          <!-- App Name -->
          <div class="form-group">
            <label for="appName" class="form-label">
              {{ $t('form.appName') }} <span class="required">*</span>
            </label>
            <div class="input-wrapper">
              <input
                id="appName"
                v-model="formData.appName"
                type="text"
                class="form-input"
                :placeholder="$t('form.appNamePlaceholder')"
                :class="{ 'error': errors.appName }"
              />
              <div class="input-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
            </div>
            <span v-if="errors.appName" class="error-message">{{ errors.appName }}</span>
          </div>

          <!-- Company Name -->
          <div class="form-group">
            <label for="companyName" class="form-label">
              {{ $t('form.companyName') }}
            </label>
            <div class="input-wrapper">
              <input
                id="companyName"
                v-model="formData.companyName"
                type="text"
                class="form-input"
                :placeholder="$t('form.companyNamePlaceholder')"
              />
              <div class="input-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M3 21H21V8L12 2L3 8V21Z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：配置选项 -->
        <div class="form-section config-options">
          <h3 class="section-title">
            <span class="section-icon">⚙️</span>
            {{ $t('form.configuration') || 'Configuration' }}
          </h3>

          <!-- Industry -->
          <div class="form-group">
            <label for="industry" class="form-label">
              {{ $t('form.industry') }} <span class="required">*</span>
            </label>
            <div class="select-wrapper">
              <select
                id="industry"
                v-model="formData.industry"
                class="form-select"
                :class="{ 'error': errors.industry }"
              >
                <option value="">{{ $t('form.industryPlaceholder') }}</option>
                <option v-for="(label, key) in industries" :key="key" :value="key">
                  {{ label }}
                </option>
              </select>
              <div class="select-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
            </div>
            <span v-if="errors.industry" class="error-message">{{ errors.industry }}</span>
          </div>

          <!-- Style Preference -->
          <div class="form-group">
            <label for="style" class="form-label">
              {{ $t('form.style') }} <span class="required">*</span>
            </label>
            <div class="select-wrapper">
              <select
                id="style"
                v-model="formData.style"
                class="form-select"
                :class="{ 'error': errors.style }"
              >
                <option value="">{{ $t('form.stylePlaceholder') }}</option>
                <option v-for="(label, key) in styles" :key="key" :value="key">
                  {{ label }}
                </option>
              </select>
              <div class="select-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
            </div>
            <span v-if="errors.style" class="error-message">{{ errors.style }}</span>
          </div>
        </div>

        <!-- 全宽：地区选择 -->
        <div class="form-section regions-section">
          <h3 class="section-title">
            <span class="section-icon">🌍</span>
            {{ $t('form.regions') }} <span class="required">*</span>
          </h3>

          <div class="regions-grid">
            <label v-for="(label, key) in regions" :key="key" class="region-card">
              <input
                type="checkbox"
                :value="key"
                v-model="formData.regions"
                class="region-checkbox"
              />
              <div class="region-content">
                <div class="region-icon">{{ getRegionIcon(key) }}</div>
                <span class="region-text">{{ label }}</span>
                <div class="region-check">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
              </div>
            </label>
          </div>
          <span v-if="errors.regions" class="error-message">{{ errors.regions }}</span>
        </div>

        <!-- 提交按钮区域 -->
        <div class="form-section submit-section">
          <button type="submit" class="submit-button" :disabled="isSubmitting">
            <div class="button-content">
              <div v-if="isSubmitting" class="loading-spinner"></div>
              <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>{{ isSubmitting ? $t('form.generating') || 'Generating...' : $t('form.generate') }}</span>
            </div>
          </button>
        </div>
      </form>
    </div>

    <!-- Bundle ID Preview -->
    <div class="preview-section">
      <BundleIdPreview :form-data="formData" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import BundleIdPreview from './BundleIdPreview.vue'

const { t } = useI18n()

const emit = defineEmits(['submit'])

const formData = ref({
  appName: '',
  industry: '',
  regions: [],
  companyName: '',
  style: ''
})

const errors = ref({})
const isSubmitting = ref(false)

// Computed properties for translated options
const industries = computed(() => {
  const industryKeys = ['technology', 'finance', 'healthcare', 'education', 'entertainment',
                       'ecommerce', 'travel', 'food', 'fitness', 'social', 'productivity',
                       'games', 'news', 'business', 'lifestyle']
  return industryKeys.reduce((acc, key) => {
    acc[key] = t(`industries.${key}`)
    return acc
  }, {})
})

const regions = computed(() => {
  const regionKeys = ['global', 'northAmerica', 'europe', 'asia', 'china', 'japan',
                     'southKorea', 'southeastAsia', 'middleEast', 'africa', 'southAmerica', 'oceania']
  return regionKeys.reduce((acc, key) => {
    acc[key] = t(`regions.${key}`)
    return acc
  }, {})
})

const styles = computed(() => {
  const styleKeys = ['marketing', 'functional', 'formal', 'creative']
  return styleKeys.reduce((acc, key) => {
    acc[key] = t(`styles.${key}`)
    return acc
  }, {})
})

// Validation
const validateForm = () => {
  errors.value = {}

  if (!formData.value.appName.trim()) {
    errors.value.appName = t('validation.appNameRequired')
  }

  if (!formData.value.industry) {
    errors.value.industry = t('validation.industryRequired')
  }

  if (formData.value.regions.length === 0) {
    errors.value.regions = t('validation.regionsRequired')
  }

  if (!formData.value.style) {
    errors.value.style = t('validation.styleRequired')
  }

  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true

  try {
    emit('submit', { ...formData.value })
  } finally {
    isSubmitting.value = false
  }
}

// 获取地区图标
const getRegionIcon = (regionKey) => {
  const icons = {
    global: '🌍',
    northAmerica: '🇺🇸',
    europe: '🇪🇺',
    asia: '🌏',
    china: '🇨🇳',
    japan: '🇯🇵',
    southKorea: '🇰🇷',
    southeastAsia: '🌴',
    middleEast: '🕌',
    africa: '🌍',
    southAmerica: '🌎',
    oceania: '🏝️'
  }
  return icons[regionKey] || '🌍'
}
</script>

<style scoped>
/* 主容器 */
.bundle-form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

/* 表单头部 */
.form-header {
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px 20px 0 0;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-bottom: none;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.form-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4, #8b5cf6);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.header-text h2 {
  color: #f8fafc;
  margin: 0 0 0.5rem 0;
  font-size: 2.25rem;
  font-weight: 700;
  background: linear-gradient(135deg, #f8fafc, #cbd5e1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  color: #cbd5e1;
  font-size: 1.125rem;
  margin: 0;
  opacity: 0.9;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

/* 表单主体 */
.form-main {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-top: none;
  border-radius: 0 0 20px 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

/* 表单区块 */
.form-section {
  background: rgba(51, 65, 85, 0.4);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(71, 85, 105, 0.3);
  transition: all 0.3s ease;
}

.form-section:hover {
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.1);
}

.regions-section {
  grid-column: 1 / -1;
}

.submit-section {
  grid-column: 1 / -1;
  background: transparent;
  border: none;
  padding: 1rem 0 0 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 0 1.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #f8fafc;
}

.section-icon {
  font-size: 1.25rem;
}

/* 表单组件 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.form-label {
  font-weight: 600;
  color: #f1f5f9;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.required {
  color: #f87171;
  font-weight: 700;
}

/* 输入框样式 */
.input-wrapper,
.select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid rgba(71, 85, 105, 0.5);
  border-radius: 12px;
  font-size: 1rem;
  background: rgba(15, 23, 42, 0.6);
  color: #f8fafc;
  transition: all 0.3s ease;
  outline: none;
}

.form-select {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem;
  border: 2px solid rgba(71, 85, 105, 0.5);
  border-radius: 12px;
  font-size: 1rem;
  background: rgba(15, 23, 42, 0.6);
  color: #f8fafc;
  transition: all 0.3s ease;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;
}

.form-input::placeholder {
  color: #94a3b8;
}

.form-input:focus,
.form-select:focus {
  border-color: #3b82f6;
  background: rgba(15, 23, 42, 0.8);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.form-input.error,
.form-select.error {
  border-color: #f87171;
  box-shadow: 0 0 0 4px rgba(248, 113, 113, 0.1);
}

.input-icon,
.select-icon {
  position: absolute;
  left: 1rem;
  color: #94a3b8;
  pointer-events: none;
  z-index: 1;
}

.select-icon {
  right: 1rem;
  left: auto;
  pointer-events: none;
}

/* 下拉框选项样式 */
.form-select option {
  background: #1e293b;
  color: #f8fafc;
  padding: 0.75rem;
  border: none;
}

.form-select option:hover,
.form-select option:focus {
  background: #334155;
}

/* 地区选择网格 */
.regions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.region-card {
  position: relative;
  cursor: pointer;
  border-radius: 12px;
  border: 2px solid rgba(71, 85, 105, 0.3);
  background: rgba(15, 23, 42, 0.4);
  transition: all 0.3s ease;
  overflow: hidden;
}

.region-card:hover {
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15);
}

.region-checkbox {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.region-checkbox:checked + .region-content {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(6, 182, 212, 0.2));
  border-color: #3b82f6;
}

.region-checkbox:checked + .region-content .region-check {
  opacity: 1;
  transform: scale(1);
}

.region-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid transparent;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.region-icon {
  font-size: 1.5rem;
  min-width: 2rem;
  text-align: center;
}

.region-text {
  flex: 1;
  color: #e2e8f0;
  font-weight: 500;
  font-size: 0.875rem;
}

.region-check {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  color: white;
  padding: 1.25rem 2rem;
  border: none;
  border-radius: 16px;
  font-size: 1.125rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
}

.submit-button:active {
  transform: translateY(-1px);
}

.submit-button:disabled {
  background: linear-gradient(135deg, #64748b, #475569);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误消息 */
.error-message {
  color: #fca5a5;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message::before {
  content: '⚠️';
  font-size: 1rem;
}

/* 预览区域 */
.preview-section {
  margin-top: 2rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .bundle-form-container {
    max-width: 800px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .regions-section {
    grid-column: 1;
  }

  .submit-section {
    grid-column: 1;
  }
}

@media (max-width: 768px) {
  .bundle-form-container {
    margin: 1rem;
  }

  .form-header {
    padding: 1.5rem;
    border-radius: 16px 16px 0 0;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .header-icon {
    width: 48px;
    height: 48px;
  }

  .header-text h2 {
    font-size: 1.75rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .form-main {
    padding: 1.5rem;
    border-radius: 0 0 16px 16px;
  }

  .form-section {
    padding: 1rem;
  }

  .regions-grid {
    grid-template-columns: 1fr;
  }

  .form-input,
  .form-select {
    padding: 0.875rem 0.875rem 0.875rem 2.5rem;
  }

  .submit-button {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .bundle-form-container {
    margin: 0.5rem;
  }

  .form-header {
    padding: 1rem;
  }

  .header-text h2 {
    font-size: 1.5rem;
  }

  .form-main {
    padding: 1rem;
  }

  .form-section {
    padding: 0.75rem;
  }

  .section-title {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .bundle-form {
    padding: 1rem;
    margin: 1rem;
  }

  .checkbox-group {
    grid-template-columns: 1fr;
  }
}
</style>
