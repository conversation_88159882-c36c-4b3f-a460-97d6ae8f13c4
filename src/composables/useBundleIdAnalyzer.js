export default function useBundleIdAnalyzer() {
  // Bundle ID 质量分析器
  
  // 评分标准
  const SCORING_CRITERIA = {
    length: {
      optimal: [15, 35], // 最佳长度范围
      acceptable: [10, 50], // 可接受范围
      weight: 0.2
    },
    readability: {
      weight: 0.25
    },
    memorability: {
      weight: 0.2
    },
    professionalism: {
      weight: 0.15
    },
    uniqueness: {
      weight: 0.1
    },
    compliance: {
      weight: 0.1
    }
  }

  // 常见的好词汇
  const POSITIVE_WORDS = [
    'pro', 'smart', 'tech', 'digital', 'modern', 'clean', 'simple',
    'secure', 'fast', 'easy', 'premium', 'elite', 'official'
  ]

  // 应该避免的词汇
  const NEGATIVE_WORDS = [
    'test', 'demo', 'temp', 'old', 'legacy', 'deprecated', 'beta'
  ]

  // 分析长度得分
  const analyzeLengthScore = (bundleId) => {
    const length = bundleId.length
    const { optimal, acceptable } = SCORING_CRITERIA.length
    
    if (length >= optimal[0] && length <= optimal[1]) {
      return 100
    } else if (length >= acceptable[0] && length <= acceptable[1]) {
      return 80
    } else if (length < acceptable[0]) {
      return Math.max(50, (length / acceptable[0]) * 80)
    } else {
      return Math.max(30, 80 - ((length - acceptable[1]) / 10) * 10)
    }
  }

  // 分析可读性得分
  const analyzeReadabilityScore = (bundleId) => {
    let score = 100
    const parts = bundleId.split('.')
    
    // 检查部分数量
    if (parts.length < 3) score -= 20
    if (parts.length > 5) score -= 15
    
    // 检查每个部分的长度
    parts.forEach(part => {
      if (part.length < 2) score -= 15
      if (part.length > 15) score -= 10
    })
    
    // 检查是否有数字结尾（通常不好）
    if (/\d+$/.test(bundleId)) score -= 10
    
    // 检查连续的相同字符
    if (/(.)\1{2,}/.test(bundleId)) score -= 15
    
    return Math.max(0, score)
  }

  // 分析记忆性得分
  const analyzeMemorabilityScore = (bundleId) => {
    let score = 100
    const parts = bundleId.split('.')
    
    // 检查是否包含积极词汇
    const hasPositiveWords = POSITIVE_WORDS.some(word => 
      bundleId.toLowerCase().includes(word)
    )
    if (hasPositiveWords) score += 10
    
    // 检查是否包含消极词汇
    const hasNegativeWords = NEGATIVE_WORDS.some(word => 
      bundleId.toLowerCase().includes(word)
    )
    if (hasNegativeWords) score -= 20
    
    // 检查韵律和节奏
    const hasGoodRhythm = parts.some(part => 
      part.length >= 4 && part.length <= 8
    )
    if (hasGoodRhythm) score += 5
    
    // 检查是否容易发音
    const hardToPronounceParts = parts.filter(part => 
      /[bcdfghjklmnpqrstvwxyz]{4,}/.test(part)
    )
    score -= hardToPronounceParts.length * 10
    
    return Math.max(0, Math.min(100, score))
  }

  // 分析专业性得分
  const analyzeProfessionalismScore = (bundleId) => {
    let score = 100
    
    // 检查TLD
    const tld = bundleId.split('.')[0]
    const professionalTlds = ['com', 'org', 'net', 'biz', 'pro']
    const creativeTlds = ['io', 'app', 'dev', 'tech', 'ai']
    
    if (professionalTlds.includes(tld)) score += 10
    else if (creativeTlds.includes(tld)) score += 5
    else score -= 5
    
    // 检查是否使用了行业相关词汇
    const industryWords = [
      'tech', 'digital', 'smart', 'pro', 'enterprise', 'business',
      'health', 'finance', 'edu', 'media', 'social'
    ]
    const hasIndustryWords = industryWords.some(word => 
      bundleId.toLowerCase().includes(word)
    )
    if (hasIndustryWords) score += 10
    
    // 检查是否过于随意
    if (/[xyz]{2,}/.test(bundleId)) score -= 15
    if (bundleId.includes('lol') || bundleId.includes('wtf')) score -= 30
    
    return Math.max(0, Math.min(100, score))
  }

  // 分析唯一性得分
  const analyzeUniquenessScore = (bundleId) => {
    let score = 100
    
    // 检查是否使用了过于常见的词汇
    const commonWords = ['app', 'test', 'demo', 'sample', 'example']
    const commonWordCount = commonWords.filter(word => 
      bundleId.toLowerCase().includes(word)
    ).length
    score -= commonWordCount * 15
    
    // 检查是否有创意元素
    const creativeElements = ['hub', 'lab', 'studio', 'forge', 'craft', 'space']
    const hasCreativeElements = creativeElements.some(word => 
      bundleId.toLowerCase().includes(word)
    )
    if (hasCreativeElements) score += 10
    
    return Math.max(0, Math.min(100, score))
  }

  // 分析合规性得分
  const analyzeComplianceScore = (bundleId) => {
    let score = 100
    
    // 检查格式合规性
    if (!/^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$/.test(bundleId)) {
      score = 0
      return score
    }
    
    // 检查长度限制
    if (bundleId.length > 100) score -= 50
    
    // 检查每个部分是否以字母开头
    const parts = bundleId.split('.')
    parts.forEach(part => {
      if (!/^[a-z]/.test(part)) score -= 20
    })
    
    // 检查是否有保留词
    const reservedWords = ['apple', 'google', 'microsoft', 'facebook', 'amazon']
    const hasReservedWords = reservedWords.some(word => 
      bundleId.toLowerCase().includes(word)
    )
    if (hasReservedWords) score -= 30
    
    return Math.max(0, score)
  }

  // 生成改进建议
  const generateSuggestions = (bundleId, scores) => {
    const suggestions = []
    
    if (scores.length < 70) {
      if (bundleId.length < 15) {
        suggestions.push({
          type: 'length',
          message: 'Consider adding more descriptive components',
          priority: 'medium'
        })
      } else if (bundleId.length > 50) {
        suggestions.push({
          type: 'length',
          message: 'Consider shortening for better readability',
          priority: 'high'
        })
      }
    }
    
    if (scores.readability < 70) {
      suggestions.push({
        type: 'readability',
        message: 'Simplify component names for better readability',
        priority: 'high'
      })
    }
    
    if (scores.memorability < 60) {
      suggestions.push({
        type: 'memorability',
        message: 'Add more memorable or meaningful words',
        priority: 'medium'
      })
    }
    
    if (scores.professionalism < 70) {
      suggestions.push({
        type: 'professionalism',
        message: 'Consider using more professional terminology',
        priority: 'medium'
      })
    }
    
    if (scores.uniqueness < 60) {
      suggestions.push({
        type: 'uniqueness',
        message: 'Add unique elements to stand out',
        priority: 'low'
      })
    }
    
    if (scores.compliance < 90) {
      suggestions.push({
        type: 'compliance',
        message: 'Ensure compliance with naming conventions',
        priority: 'high'
      })
    }
    
    return suggestions
  }

  // 主分析函数
  const analyze = (bundleId) => {
    const scores = {
      length: analyzeLengthScore(bundleId),
      readability: analyzeReadabilityScore(bundleId),
      memorability: analyzeMemorabilityScore(bundleId),
      professionalism: analyzeProfessionalismScore(bundleId),
      uniqueness: analyzeUniquenessScore(bundleId),
      compliance: analyzeComplianceScore(bundleId)
    }
    
    // 计算总分
    const totalScore = Object.keys(scores).reduce((total, key) => {
      const weight = SCORING_CRITERIA[key]?.weight || 0.1
      return total + (scores[key] * weight)
    }, 0)
    
    // 生成等级
    let grade = 'F'
    if (totalScore >= 90) grade = 'A+'
    else if (totalScore >= 85) grade = 'A'
    else if (totalScore >= 80) grade = 'A-'
    else if (totalScore >= 75) grade = 'B+'
    else if (totalScore >= 70) grade = 'B'
    else if (totalScore >= 65) grade = 'B-'
    else if (totalScore >= 60) grade = 'C+'
    else if (totalScore >= 55) grade = 'C'
    else if (totalScore >= 50) grade = 'C-'
    else if (totalScore >= 40) grade = 'D'
    
    const suggestions = generateSuggestions(bundleId, scores)
    
    return {
      bundleId,
      totalScore: Math.round(totalScore),
      grade,
      scores,
      suggestions,
      analysis: {
        length: bundleId.length,
        parts: bundleId.split('.').length,
        tld: bundleId.split('.')[0],
        hasNumbers: /\d/.test(bundleId),
        complexity: bundleId.split('.').reduce((acc, part) => acc + part.length, 0) / bundleId.split('.').length
      }
    }
  }

  // 批量分析
  const analyzeMultiple = (bundleIds) => {
    return bundleIds.map(id => analyze(id)).sort((a, b) => b.totalScore - a.totalScore)
  }

  return {
    analyze,
    analyzeMultiple
  }
}
