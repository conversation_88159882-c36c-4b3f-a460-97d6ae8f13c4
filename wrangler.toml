# Cloudflare Pages configuration
name = "bundle-id-generator"
compatibility_date = "2024-01-01"

[env.production]
# Production environment settings
# Add any environment-specific configurations here

[build]
# Build configuration for Cloudflare Pages
command = "npm run build"
publish = "dist"

# Optional: Custom headers for security and performance
[[headers]]
for = "/*"
[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
X-XSS-Protection = "1; mode=block"
Referrer-Policy = "strict-origin-when-cross-origin"
Permissions-Policy = "camera=(), microphone=(), geolocation=()"

# Cache static assets
[[headers]]
for = "/assets/*"
[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

# Cache HTML with shorter duration
[[headers]]
for = "/*.html"
[headers.values]
Cache-Control = "public, max-age=3600"

# Service Worker caching
[[headers]]
for = "/sw.js"
[headers.values]
Cache-Control = "public, max-age=0, must-revalidate"
